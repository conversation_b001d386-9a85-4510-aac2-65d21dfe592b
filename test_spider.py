# -*- coding: utf-8 -*-
"""
虎嗅爬虫测试脚本
"""

import sys
import time
from datetime import datetime
from huxiu_spider import <PERSON><PERSON>u<PERSON>pid<PERSON>

def test_basic_functionality():
    """测试基本功能"""
    print("=" * 50)
    print("开始测试虎嗅爬虫基本功能")
    print("=" * 50)
    
    # 创建爬虫实例
    spider = HuxiuSpider()
    
    try:
        # 测试1: 爬取文章列表
        print("\n1. 测试文章列表爬取...")
        articles = spider.crawl_article_list()
        
        if articles:
            print(f"✓ 成功爬取 {len(articles)} 篇文章")
            
            # 显示前3篇文章信息
            for i, article in enumerate(articles[:3], 1):
                print(f"\n文章 {i}:")
                print(f"  标题: {article.get('title', '无标题')}")
                print(f"  作者: {article.get('author', '未知')}")
                print(f"  时间: {article.get('publish_time', '未知')}")
                print(f"  链接: {article.get('url', '')}")
        else:
            print("✗ 文章列表爬取失败")
            return False
        
        # 测试2: 过滤今日文章
        print("\n2. 测试今日文章过滤...")
        today_articles = spider.crawl_today_news()
        print(f"✓ 找到 {len(today_articles)} 篇今日文章")
        
        # 测试3: 爬取文章详情（只测试1篇）
        if articles:
            print("\n3. 测试文章详情爬取...")
            test_article = articles[0]
            detail = spider.crawl_article_detail(test_article['url'])
            
            if detail:
                print("✓ 文章详情爬取成功")
                print(f"  标题: {detail.get('title', '无标题')}")
                print(f"  内容长度: {detail.get('content_length', 0)} 字符")
                print(f"  作者: {detail.get('author', '未知')}")
            else:
                print("✗ 文章详情爬取失败")
        
        # 测试4: 数据保存
        print("\n4. 测试数据保存...")
        
        # JSON格式
        json_file = spider.save_to_json(articles[:5], "test_articles.json")
        if json_file:
            print(f"✓ JSON文件保存成功: {json_file}")
        
        # CSV格式
        csv_file = spider.save_to_csv(articles[:5], "test_articles.csv")
        if csv_file:
            print(f"✓ CSV文件保存成功: {csv_file}")
        
        # 摘要报告
        summary_file = spider.save_summary_report(articles[:5], "test_summary.txt")
        if summary_file:
            print(f"✓ 摘要报告保存成功: {summary_file}")
        
        # 测试5: 统计信息
        print("\n5. 测试统计信息...")
        stats = spider.get_statistics()
        print("✓ 统计信息:")
        for key, value in stats.items():
            print(f"  {key}: {value}")
        
        print("\n" + "=" * 50)
        print("✓ 所有基本功能测试通过!")
        print("=" * 50)
        return True
        
    except Exception as e:
        print(f"\n✗ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_performance():
    """测试性能"""
    print("\n" + "=" * 50)
    print("开始性能测试")
    print("=" * 50)
    
    spider = HuxiuSpider()
    
    # 测试请求速度
    start_time = time.time()
    articles = spider.crawl_article_list()
    end_time = time.time()
    
    if articles:
        elapsed_time = end_time - start_time
        articles_per_second = len(articles) / elapsed_time
        
        print(f"✓ 性能测试结果:")
        print(f"  爬取文章数: {len(articles)}")
        print(f"  耗时: {elapsed_time:.2f} 秒")
        print(f"  平均速度: {articles_per_second:.2f} 文章/秒")
        
        # 反爬虫机制状态
        anti_spider_status = spider.anti_spider.get_status()
        print(f"  请求次数: {anti_spider_status['request_count']}")
        print(f"  当前延时范围: {anti_spider_status['min_interval']:.1f}-{anti_spider_status['max_interval']:.1f}秒")
    else:
        print("✗ 性能测试失败 - 无法获取文章")

def test_error_handling():
    """测试错误处理"""
    print("\n" + "=" * 50)
    print("开始错误处理测试")
    print("=" * 50)
    
    spider = HuxiuSpider()
    
    # 测试无效URL
    print("1. 测试无效URL处理...")
    invalid_response = spider._make_request("https://invalid-url-test.com")
    if invalid_response is None:
        print("✓ 无效URL处理正确")
    else:
        print("✗ 无效URL处理异常")
    
    # 测试空数据保存
    print("\n2. 测试空数据保存...")
    empty_json = spider.save_to_json([], "empty_test.json")
    empty_csv = spider.save_to_csv([], "empty_test.csv")
    
    if empty_json and empty_csv:
        print("✓ 空数据保存处理正确")
    else:
        print("✗ 空数据保存处理异常")

def main():
    """主测试函数"""
    print(f"虎嗅爬虫测试开始 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 基本功能测试
    basic_test_passed = test_basic_functionality()
    
    if basic_test_passed:
        # 性能测试
        test_performance()
        
        # 错误处理测试
        test_error_handling()
        
        print(f"\n🎉 所有测试完成! - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    else:
        print(f"\n❌ 基本功能测试失败，跳过其他测试")
        sys.exit(1)

if __name__ == "__main__":
    main()
