# -*- coding: utf-8 -*-
"""
虎嗅网页面元素选择器定义
基于页面分析结果定义的CSS选择器
"""

# 文章列表页选择器
ARTICLE_LIST_SELECTORS = {
    # 文章链接 - 基于观察到的页面结构
    'article_links': 'a[href*="/article/"]',
    
    # 文章标题 - 通常在链接内部
    'article_title': 'a[href*="/article/"] h3, a[href*="/article/"] .title, a[href*="/article/"]',
    
    # 作者链接
    'author_links': 'a[href*="/member/"]',
    
    # 时间信息 - 查找包含时间关键词的元素
    'time_elements': '*:contains("分钟前"), *:contains("小时前"), *:contains("天前"), *:contains("今天"), *:contains("昨天")',
    
    # 评论数 - 通常在文章信息区域
    'comment_count': '*:contains("评论"), .comment-count, .comments',
    
    # 文章摘要
    'article_summary': '.summary, .excerpt, .description',
    
    # 文章容器 - 可能的文章项容器
    'article_container': '.article-item, .news-item, .post-item, article, .content-item'
}

# 文章详情页选择器
ARTICLE_DETAIL_SELECTORS = {
    # 文章标题
    'title': 'h1, .article-title, .post-title, .title',
    
    # 文章内容
    'content': '.article-content, .post-content, .content, .article-body, .post-body',
    
    # 作者信息
    'author': '.author, .author-name, a[href*="/member/"]',
    
    # 发布时间
    'publish_time': 'time, .publish-time, .post-time, .date, .time',
    
    # 阅读数
    'view_count': '.view-count, .read-count, *:contains("阅读")',
    
    # 评论数
    'comment_count': '.comment-count, *:contains("评论")',
    
    # 标签
    'tags': '.tags, .tag, .category',
    
    # 文章摘要/描述
    'summary': '.summary, .excerpt, .description, meta[name="description"]'
}

# 通用选择器
COMMON_SELECTORS = {
    # 分页
    'pagination': '.pagination, .pager, .page-nav',
    'next_page': '.next, .page-next, a:contains("下一页")',
    'prev_page': '.prev, .page-prev, a:contains("上一页")',
    
    # 加载更多
    'load_more': '.load-more, .more, a:contains("加载更多")',
    
    # 错误页面
    'error_page': '.error, .not-found, .404',
    
    # 登录要求
    'login_required': '.login, .sign-in, *:contains("登录")'
}

# 数据提取规则
EXTRACTION_RULES = {
    'article_list': {
        'container': 'body',  # 整个页面作为容器
        'items': {
            'title': {
                'selector': 'a[href*="/article/"]',
                'attr': 'text',
                'required': True
            },
            'url': {
                'selector': 'a[href*="/article/"]',
                'attr': 'href',
                'required': True,
                'process': lambda x: x if x.startswith('http') else f"https://www.huxiu.com{x}"
            },
            'author': {
                'selector': 'a[href*="/member/"]',
                'attr': 'text',
                'required': False
            },
            'author_url': {
                'selector': 'a[href*="/member/"]',
                'attr': 'href',
                'required': False,
                'process': lambda x: x if x.startswith('http') else f"https://www.huxiu.com{x}"
            },
            'publish_time': {
                'selector': '*',
                'attr': 'text',
                'required': False,
                'filter': lambda x: any(keyword in x for keyword in ['分钟前', '小时前', '天前', '今天', '昨天']) if x else False
            }
        }
    },
    
    'article_detail': {
        'container': 'body',
        'items': {
            'title': {
                'selector': 'h1, .article-title, .post-title',
                'attr': 'text',
                'required': True
            },
            'content': {
                'selector': '.article-content, .post-content, .content',
                'attr': 'text',
                'required': True
            },
            'author': {
                'selector': '.author, a[href*="/member/"]',
                'attr': 'text',
                'required': False
            },
            'publish_time': {
                'selector': 'time, .publish-time, .date',
                'attr': 'text',
                'required': False
            },
            'view_count': {
                'selector': '.view-count, *:contains("阅读")',
                'attr': 'text',
                'required': False
            },
            'comment_count': {
                'selector': '.comment-count, *:contains("评论")',
                'attr': 'text',
                'required': False
            }
        }
    }
}

# 数据清洗规则
CLEANING_RULES = {
    'title': {
        'strip': True,
        'remove_extra_spaces': True,
        'max_length': 200
    },
    'content': {
        'strip': True,
        'remove_extra_spaces': True,
        'remove_html_tags': True
    },
    'author': {
        'strip': True,
        'remove_extra_spaces': True
    },
    'url': {
        'strip': True,
        'validate_url': True
    }
}
