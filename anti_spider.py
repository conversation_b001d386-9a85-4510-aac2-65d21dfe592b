# -*- coding: utf-8 -*-
"""
反爬虫机制模块
"""

import time
import random
import requests
from typing import Dict, List, Optional
from fake_useragent import UserAgent

class AntiSpiderHandler:
    """反爬虫处理器"""
    
    def __init__(self):
        self.ua = UserAgent()
        self.session = requests.Session()
        self.request_count = 0
        self.last_request_time = 0
        
        # 代理池（如果需要）
        self.proxy_pool = []
        self.current_proxy_index = 0
        
        # 请求间隔控制
        self.min_interval = 1.0
        self.max_interval = 3.0
        
        # 失败重试配置
        self.max_retries = 3
        self.retry_delay = 2.0
    
    def get_random_headers(self) -> Dict[str, str]:
        """获取随机请求头"""
        headers = {
            'User-Agent': self.ua.random,
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': random.choice([
                'zh-C<PERSON>,zh;q=0.9,en;q=0.8',
                'zh-CN,zh;q=0.8,en;q=0.7',
                'en-US,en;q=0.9,zh;q=0.8'
            ]),
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': random.choice(['none', 'same-origin', 'cross-site']),
            'Cache-Control': random.choice(['max-age=0', 'no-cache']),
            'DNT': str(random.randint(0, 1))
        }
        
        # 随机添加一些可选头部
        if random.random() > 0.5:
            headers['Referer'] = 'https://www.google.com/'
        
        if random.random() > 0.7:
            headers['X-Forwarded-For'] = self._generate_fake_ip()
        
        return headers
    
    def _generate_fake_ip(self) -> str:
        """生成虚假IP地址"""
        return f"{random.randint(1, 255)}.{random.randint(1, 255)}.{random.randint(1, 255)}.{random.randint(1, 255)}"
    
    def adaptive_delay(self):
        """自适应延时"""
        current_time = time.time()
        
        # 基于请求频率调整延时
        if self.request_count > 0:
            time_since_last = current_time - self.last_request_time
            if time_since_last < self.min_interval:
                additional_delay = self.min_interval - time_since_last
                time.sleep(additional_delay)
        
        # 随机延时
        delay = random.uniform(self.min_interval, self.max_interval)
        
        # 根据请求次数增加延时
        if self.request_count > 50:
            delay *= 1.5
        elif self.request_count > 100:
            delay *= 2.0
        
        time.sleep(delay)
        
        self.last_request_time = time.time()
        self.request_count += 1
    
    def add_proxy(self, proxy: str):
        """添加代理到代理池"""
        self.proxy_pool.append(proxy)
    
    def get_current_proxy(self) -> Optional[Dict[str, str]]:
        """获取当前代理"""
        if not self.proxy_pool:
            return None
        
        proxy = self.proxy_pool[self.current_proxy_index]
        return {
            'http': proxy,
            'https': proxy
        }
    
    def rotate_proxy(self):
        """轮换代理"""
        if self.proxy_pool:
            self.current_proxy_index = (self.current_proxy_index + 1) % len(self.proxy_pool)
    
    def handle_rate_limit(self, response: requests.Response) -> bool:
        """处理限流响应"""
        if response.status_code == 429:  # Too Many Requests
            retry_after = response.headers.get('Retry-After')
            if retry_after:
                wait_time = int(retry_after)
            else:
                wait_time = random.uniform(60, 120)  # 默认等待1-2分钟
            
            print(f"遇到限流，等待 {wait_time} 秒...")
            time.sleep(wait_time)
            return True
        
        return False
    
    def handle_blocked_response(self, response: requests.Response) -> bool:
        """处理被阻止的响应"""
        # 检查常见的反爬虫响应
        blocked_indicators = [
            'blocked', 'forbidden', 'access denied', 
            'robot', 'captcha', '验证码', '人机验证'
        ]
        
        response_text = response.text.lower()
        for indicator in blocked_indicators:
            if indicator in response_text:
                print(f"检测到反爬虫机制: {indicator}")
                # 轮换代理
                self.rotate_proxy()
                # 增加延时
                self.min_interval *= 1.5
                self.max_interval *= 1.5
                return True
        
        return False
    
    def make_safe_request(self, url: str, **kwargs) -> Optional[requests.Response]:
        """安全的HTTP请求"""
        for attempt in range(self.max_retries):
            try:
                # 自适应延时
                self.adaptive_delay()
                
                # 设置请求头
                headers = self.get_random_headers()
                kwargs['headers'] = headers
                
                # 设置代理
                proxy = self.get_current_proxy()
                if proxy:
                    kwargs['proxies'] = proxy
                
                # 设置超时
                kwargs.setdefault('timeout', 30)
                
                # 发送请求
                response = self.session.get(url, **kwargs)
                
                # 处理限流
                if self.handle_rate_limit(response):
                    continue
                
                # 处理反爬虫
                if self.handle_blocked_response(response):
                    continue
                
                # 检查响应状态
                response.raise_for_status()
                
                return response
                
            except requests.exceptions.RequestException as e:
                print(f"请求失败 (尝试 {attempt + 1}/{self.max_retries}): {e}")
                
                if attempt < self.max_retries - 1:
                    # 轮换代理
                    self.rotate_proxy()
                    # 增加重试延时
                    time.sleep(self.retry_delay * (attempt + 1))
                else:
                    print(f"请求最终失败: {url}")
                    return None
        
        return None
    
    def reset_counters(self):
        """重置计数器"""
        self.request_count = 0
        self.last_request_time = 0
        self.min_interval = 1.0
        self.max_interval = 3.0
    
    def get_status(self) -> Dict[str, any]:
        """获取状态信息"""
        return {
            'request_count': self.request_count,
            'proxy_count': len(self.proxy_pool),
            'current_proxy_index': self.current_proxy_index,
            'min_interval': self.min_interval,
            'max_interval': self.max_interval
        }
