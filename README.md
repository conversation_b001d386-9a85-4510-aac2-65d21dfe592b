# 虎嗅新闻爬虫

这是一个用于爬取虎嗅网新闻内容的Python爬虫项目。

## 功能特性

- 爬取虎嗅网今日新闻列表
- 获取文章详细内容
- 支持多种数据输出格式（JSON、CSV）
- 内置反爬虫机制
- 错误处理和重试机制

## 安装依赖

```bash
pip install -r requirements.txt
```

## 使用方法

```python
from huxiu_spider import HuxiuSpider

# 创建爬虫实例
spider = HuxiuSpider()

# 爬取今日新闻
news_data = spider.crawl_today_news()

# 保存数据
spider.save_to_json(news_data, 'huxiu_news.json')
spider.save_to_csv(news_data, 'huxiu_news.csv')
```

## 项目结构

```
虎嗅新闻爬虫/
├── huxiu_spider.py      # 主爬虫类
├── utils.py             # 工具函数
├── config.py            # 配置文件
├── requirements.txt     # 依赖包列表
├── README.md           # 项目说明
└── data/               # 数据输出目录
```

## 注意事项

- 请遵守网站的robots.txt协议
- 合理控制爬取频率，避免对服务器造成压力
- 仅用于学习和研究目的
