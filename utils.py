# -*- coding: utf-8 -*-
"""
工具函数模块
"""

import re
import time
import random
import logging
from urllib.parse import urljoin, urlparse
from datetime import datetime, timedelta
from typing import Optional, List, Dict, Any

def setup_logger(name: str = "huxiu_spider", level: str = "INFO") -> logging.Logger:
    """设置日志记录器"""
    logger = logging.getLogger(name)
    logger.setLevel(getattr(logging, level.upper()))
    
    if not logger.handlers:
        handler = logging.StreamHandler()
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        handler.setFormatter(formatter)
        logger.addHandler(handler)
    
    return logger

def clean_text(text: str) -> str:
    """清理文本内容"""
    if not text:
        return ""
    
    # 移除多余的空白字符
    text = re.sub(r'\s+', ' ', text.strip())
    
    # 移除特殊字符
    text = re.sub(r'[\r\n\t]', ' ', text)
    
    return text.strip()

def extract_numbers(text: str) -> Optional[int]:
    """从文本中提取数字"""
    if not text:
        return None
    
    # 查找数字
    numbers = re.findall(r'\d+', text)
    if numbers:
        return int(numbers[0])
    
    return None

def parse_relative_time(time_str: str) -> Optional[datetime]:
    """解析相对时间字符串"""
    if not time_str:
        return None
    
    now = datetime.now()
    time_str = time_str.strip()
    
    # 分钟前
    if '分钟前' in time_str:
        minutes = extract_numbers(time_str)
        if minutes:
            return now - timedelta(minutes=minutes)
    
    # 小时前
    elif '小时前' in time_str:
        hours = extract_numbers(time_str)
        if hours:
            return now - timedelta(hours=hours)
    
    # 天前
    elif '天前' in time_str:
        days = extract_numbers(time_str)
        if days:
            return now - timedelta(days=days)
    
    # 今天
    elif '今天' in time_str:
        return now.replace(hour=0, minute=0, second=0, microsecond=0)
    
    # 昨天
    elif '昨天' in time_str:
        return (now - timedelta(days=1)).replace(hour=0, minute=0, second=0, microsecond=0)
    
    return None

def is_valid_url(url: str) -> bool:
    """验证URL是否有效"""
    if not url:
        return False
    
    try:
        result = urlparse(url)
        return all([result.scheme, result.netloc])
    except Exception:
        return False

def normalize_url(url: str, base_url: str = "https://www.huxiu.com") -> str:
    """标准化URL"""
    if not url:
        return ""
    
    # 如果是完整URL，直接返回
    if url.startswith(('http://', 'https://')):
        return url
    
    # 如果是相对URL，拼接base_url
    return urljoin(base_url, url)

def random_delay(min_seconds: float = 1.0, max_seconds: float = 3.0) -> None:
    """随机延时"""
    delay = random.uniform(min_seconds, max_seconds)
    time.sleep(delay)

def extract_article_id(url: str) -> Optional[str]:
    """从URL中提取文章ID"""
    if not url:
        return None
    
    # 匹配 /article/数字.html 格式
    match = re.search(r'/article/(\d+)\.html', url)
    if match:
        return match.group(1)
    
    # 匹配 /article/数字 格式
    match = re.search(r'/article/(\d+)', url)
    if match:
        return match.group(1)
    
    return None

def filter_today_articles(articles: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """过滤出今日文章"""
    today_articles = []
    now = datetime.now()
    today_start = now.replace(hour=0, minute=0, second=0, microsecond=0)
    
    for article in articles:
        publish_time = article.get('publish_time')
        if isinstance(publish_time, str):
            publish_time = parse_relative_time(publish_time)
        
        if publish_time and publish_time >= today_start:
            today_articles.append(article)
    
    return today_articles

def safe_extract(soup, selector: str, attr: str = 'text', default: str = "") -> str:
    """安全提取元素内容"""
    try:
        if attr == 'text':
            element = soup.select_one(selector)
            return clean_text(element.get_text()) if element else default
        else:
            element = soup.select_one(selector)
            return element.get(attr, default) if element else default
    except Exception:
        return default

def batch_process(items: List[Any], batch_size: int = 10, delay: float = 1.0):
    """批量处理数据"""
    for i in range(0, len(items), batch_size):
        batch = items[i:i + batch_size]
        yield batch
        if i + batch_size < len(items):  # 不是最后一批
            time.sleep(delay)

def validate_article_data(article: Dict[str, Any]) -> bool:
    """验证文章数据的完整性"""
    required_fields = ['title', 'url']
    
    for field in required_fields:
        if not article.get(field):
            return False
    
    # 验证URL格式
    if not is_valid_url(article.get('url')):
        return False
    
    return True

def format_file_size(size_bytes: int) -> str:
    """格式化文件大小"""
    if size_bytes == 0:
        return "0B"
    
    size_names = ["B", "KB", "MB", "GB"]
    i = 0
    while size_bytes >= 1024 and i < len(size_names) - 1:
        size_bytes /= 1024.0
        i += 1
    
    return f"{size_bytes:.1f}{size_names[i]}"
