# -*- coding: utf-8 -*-
"""
页面结构分析脚本
"""

import requests
from bs4 import BeautifulSoup
import json

def analyze_huxiu_page():
    """分析虎嗅网页面结构"""
    url = "https://www.huxiu.com/article/"
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Accept-Encoding': 'gzip, deflate, br',
        'Connection': 'keep-alive',
    }
    
    try:
        response = requests.get(url, headers=headers, timeout=30)
        response.raise_for_status()
        response.encoding = 'utf-8'
        
        soup = BeautifulSoup(response.text, 'html.parser')
        
        print("=== 页面标题 ===")
        print(soup.title.text if soup.title else "无标题")
        
        print("\n=== 查找文章链接 ===")
        # 查找所有包含 /article/ 的链接
        article_links = soup.find_all('a', href=lambda x: x and '/article/' in x and x.startswith('/article/'))
        print(f"找到 {len(article_links)} 个文章链接")
        
        # 分析前几个链接的结构
        for i, link in enumerate(article_links[:5]):
            print(f"\n--- 文章 {i+1} ---")
            print(f"链接: {link.get('href')}")
            print(f"文本: {link.get_text().strip()}")
            
            # 查找父元素
            parent = link.parent
            if parent:
                print(f"父元素标签: {parent.name}")
                print(f"父元素类名: {parent.get('class', [])}")
                
                # 查找祖父元素
                grandparent = parent.parent
                if grandparent:
                    print(f"祖父元素标签: {grandparent.name}")
                    print(f"祖父元素类名: {grandparent.get('class', [])}")
        
        print("\n=== 查找可能的文章容器 ===")
        # 查找可能包含文章列表的容器
        possible_containers = soup.find_all(['div', 'section', 'article'], class_=True)
        
        container_info = {}
        for container in possible_containers:
            class_names = ' '.join(container.get('class', []))
            if class_names not in container_info:
                container_info[class_names] = 0
            container_info[class_names] += 1
        
        # 显示出现频率最高的类名
        sorted_containers = sorted(container_info.items(), key=lambda x: x[1], reverse=True)
        print("出现频率最高的容器类名:")
        for class_name, count in sorted_containers[:10]:
            print(f"  {class_name}: {count}次")
        
        print("\n=== 查找时间信息 ===")
        # 查找可能包含时间的元素
        time_elements = soup.find_all(['time', 'span'], string=lambda x: x and ('分钟前' in x or '小时前' in x or '天前' in x))
        print(f"找到 {len(time_elements)} 个时间元素")
        for i, elem in enumerate(time_elements[:3]):
            print(f"时间 {i+1}: {elem.get_text().strip()}")
            print(f"  标签: {elem.name}")
            print(f"  类名: {elem.get('class', [])}")
        
        print("\n=== 查找作者信息 ===")
        # 查找可能包含作者的链接
        author_links = soup.find_all('a', href=lambda x: x and '/member/' in x)
        print(f"找到 {len(author_links)} 个作者链接")
        for i, link in enumerate(author_links[:3]):
            print(f"作者 {i+1}: {link.get_text().strip()}")
            print(f"  链接: {link.get('href')}")
        
    except Exception as e:
        print(f"分析页面时出错: {e}")

if __name__ == "__main__":
    analyze_huxiu_page()
