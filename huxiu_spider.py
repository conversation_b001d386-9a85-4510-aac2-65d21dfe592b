# -*- coding: utf-8 -*-
"""
虎嗅网新闻爬虫主类
"""

import os
import json
import csv
import requests
import random
import pandas as pd
from datetime import datetime
from typing import List, Dict, Any, Optional
from bs4 import BeautifulSoup
from fake_useragent import UserAgent

from config import *
from utils import *
from selectors import *
from anti_spider import AntiSpiderHandler

class HuxiuSpider:
    """虎嗅网新闻爬虫"""
    
    def __init__(self):
        self.session = requests.Session()
        self.ua = UserAgent()
        self.logger = setup_logger()
        self.articles_data = []
        self.anti_spider = AntiSpiderHandler()

        # 设置默认请求头
        self.session.headers.update(DEFAULT_HEADERS)

        # 创建输出目录
        os.makedirs(OUTPUT_DIR, exist_ok=True)
    
    def _get_random_headers(self) -> Dict[str, str]:
        """获取随机请求头"""
        headers = DEFAULT_HEADERS.copy()
        headers['User-Agent'] = random.choice(USER_AGENTS)
        return headers
    
    def _make_request(self, url: str, **kwargs) -> Optional[requests.Response]:
        """发送HTTP请求（使用增强的反爬虫机制）"""
        self.logger.info(f"请求URL: {url}")

        response = self.anti_spider.make_safe_request(url, **kwargs)

        if response:
            self.logger.info(f"请求成功: {url}")
        else:
            self.logger.error(f"请求失败: {url}")

        return response
    
    def _parse_article_list(self, html: str) -> List[Dict[str, Any]]:
        """解析文章列表页面"""
        soup = BeautifulSoup(html, 'html.parser')
        articles = []
        
        # 查找所有文章链接
        article_links = soup.find_all('a', href=lambda x: x and '/article/' in x)
        
        self.logger.info(f"找到 {len(article_links)} 个文章链接")
        
        processed_urls = set()  # 避免重复
        
        for link in article_links:
            try:
                # 提取基本信息
                title = clean_text(link.get_text())
                url = normalize_url(link.get('href'))
                
                # 跳过空标题或重复URL
                if not title or url in processed_urls:
                    continue
                
                processed_urls.add(url)
                
                # 查找相关的作者和时间信息
                author = ""
                publish_time = ""
                
                # 在链接的父容器中查找作者信息
                container = link.parent
                for _ in range(3):  # 向上查找3层
                    if container:
                        author_link = container.find('a', href=lambda x: x and '/member/' in x)
                        if author_link:
                            author = clean_text(author_link.get_text())
                            break
                        container = container.parent
                
                # 查找时间信息
                container = link.parent
                for _ in range(3):  # 向上查找3层
                    if container:
                        time_text = container.get_text()
                        if any(keyword in time_text for keyword in ['分钟前', '小时前', '天前']):
                            # 提取时间部分
                            import re
                            time_match = re.search(r'(\d+(?:分钟|小时|天)前)', time_text)
                            if time_match:
                                publish_time = time_match.group(1)
                                break
                        container = container.parent
                
                article_data = {
                    'title': title,
                    'url': url,
                    'author': author,
                    'publish_time': publish_time,
                    'article_id': extract_article_id(url),
                    'crawl_time': datetime.now().isoformat()
                }
                
                if validate_article_data(article_data):
                    articles.append(article_data)
                
            except Exception as e:
                self.logger.warning(f"解析文章链接时出错: {e}")
                continue
        
        self.logger.info(f"成功解析 {len(articles)} 篇文章")
        return articles
    
    def crawl_article_list(self, url: str = ARTICLE_LIST_URL) -> List[Dict[str, Any]]:
        """爬取文章列表"""
        self.logger.info(f"开始爬取文章列表: {url}")
        
        response = self._make_request(url)
        if not response:
            return []
        
        response.encoding = 'utf-8'
        articles = self._parse_article_list(response.text)
        
        self.articles_data.extend(articles)
        return articles
    
    def crawl_today_news(self) -> List[Dict[str, Any]]:
        """爬取今日新闻"""
        self.logger.info("开始爬取今日新闻")
        
        # 爬取文章列表
        articles = self.crawl_article_list()
        
        # 过滤今日文章
        today_articles = filter_today_articles(articles)
        
        self.logger.info(f"找到 {len(today_articles)} 篇今日文章")
        return today_articles
    
    def save_to_json(self, data: List[Dict[str, Any]], filename: str = None) -> str:
        """保存数据为JSON格式"""
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"huxiu_news_{timestamp}.json"
        
        filepath = os.path.join(OUTPUT_DIR, filename)
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=JSON_INDENT)
        
        self.logger.info(f"数据已保存到: {filepath}")
        return filepath
    
    def save_to_csv(self, data: List[Dict[str, Any]], filename: str = None) -> str:
        """保存数据为CSV格式"""
        if not data:
            self.logger.warning("没有数据可保存")
            return ""
        
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"huxiu_news_{timestamp}.csv"
        
        filepath = os.path.join(OUTPUT_DIR, filename)
        
        # 获取所有字段名
        fieldnames = set()
        for item in data:
            fieldnames.update(item.keys())
        fieldnames = sorted(list(fieldnames))
        
        with open(filepath, 'w', newline='', encoding=CSV_ENCODING) as f:
            writer = csv.DictWriter(f, fieldnames=fieldnames)
            writer.writeheader()
            writer.writerows(data)
        
        self.logger.info(f"数据已保存到: {filepath}")
        return filepath

    def save_to_excel(self, data: List[Dict[str, Any]], filename: str = None) -> str:
        """保存数据为Excel格式"""
        if not data:
            self.logger.warning("没有数据可保存")
            return ""

        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"huxiu_news_{timestamp}.xlsx"

        filepath = os.path.join(OUTPUT_DIR, filename)

        try:
            df = pd.DataFrame(data)
            df.to_excel(filepath, index=False, engine='openpyxl')
            self.logger.info(f"数据已保存到: {filepath}")
            return filepath
        except Exception as e:
            self.logger.error(f"保存Excel文件失败: {e}")
            return ""

    def save_summary_report(self, data: List[Dict[str, Any]], filename: str = None) -> str:
        """生成并保存数据摘要报告"""
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"huxiu_summary_{timestamp}.txt"

        filepath = os.path.join(OUTPUT_DIR, filename)

        stats = self.get_statistics()

        report = f"""
虎嗅新闻爬取摘要报告
生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
{'='*50}

数据统计:
- 总文章数: {stats.get('total_articles', 0)}
- 有作者信息的文章: {stats.get('articles_with_author', 0)}
- 有时间信息的文章: {stats.get('articles_with_time', 0)}
- 有内容的文章: {stats.get('articles_with_content', 0)}
- 独特作者数: {stats.get('unique_authors', 0)}
- 总内容长度: {format_file_size(stats.get('total_content_length', 0))}

文章列表:
"""

        for i, article in enumerate(data[:20], 1):  # 只显示前20篇
            report += f"\n{i}. {article.get('title', '无标题')}\n"
            report += f"   作者: {article.get('author', '未知')}\n"
            report += f"   时间: {article.get('publish_time', '未知')}\n"
            report += f"   链接: {article.get('url', '')}\n"
            if article.get('content'):
                content_preview = article['content'][:100] + "..." if len(article['content']) > 100 else article['content']
                report += f"   内容预览: {content_preview}\n"

        if len(data) > 20:
            report += f"\n... 还有 {len(data) - 20} 篇文章未显示\n"

        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(report)

        self.logger.info(f"摘要报告已保存到: {filepath}")
        return filepath

    def export_all_formats(self, data: List[Dict[str, Any]], base_filename: str = None) -> Dict[str, str]:
        """导出所有格式的数据文件"""
        if base_filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            base_filename = f"huxiu_news_{timestamp}"

        exported_files = {}

        # JSON格式
        json_file = self.save_to_json(data, f"{base_filename}.json")
        if json_file:
            exported_files['json'] = json_file

        # CSV格式
        csv_file = self.save_to_csv(data, f"{base_filename}.csv")
        if csv_file:
            exported_files['csv'] = csv_file

        # Excel格式
        excel_file = self.save_to_excel(data, f"{base_filename}.xlsx")
        if excel_file:
            exported_files['excel'] = excel_file

        # 摘要报告
        summary_file = self.save_summary_report(data, f"{base_filename}_summary.txt")
        if summary_file:
            exported_files['summary'] = summary_file

        self.logger.info(f"已导出 {len(exported_files)} 种格式的文件")
        return exported_files

    def _parse_article_detail(self, html: str, url: str) -> Dict[str, Any]:
        """解析文章详情页面"""
        soup = BeautifulSoup(html, 'html.parser')

        # 提取文章标题
        title = ""
        for selector in ['h1', '.article-title', '.post-title', '.title']:
            title_elem = soup.select_one(selector)
            if title_elem:
                title = clean_text(title_elem.get_text())
                break

        # 提取文章内容
        content = ""
        for selector in ['.article-content', '.post-content', '.content', '.article-body']:
            content_elem = soup.select_one(selector)
            if content_elem:
                # 移除脚本和样式标签
                for script in content_elem(["script", "style"]):
                    script.decompose()
                content = clean_text(content_elem.get_text())
                break

        # 提取作者信息
        author = ""
        author_links = soup.find_all('a', href=lambda x: x and '/member/' in x)
        if author_links:
            author = clean_text(author_links[0].get_text())

        # 提取发布时间
        publish_time = ""
        for selector in ['time', '.publish-time', '.post-time', '.date', '.time']:
            time_elem = soup.select_one(selector)
            if time_elem:
                publish_time = clean_text(time_elem.get_text())
                break

        # 提取阅读数
        view_count = ""
        view_elements = soup.find_all(string=lambda x: x and '阅读' in x)
        if view_elements:
            for elem in view_elements:
                parent = elem.parent
                if parent:
                    text = clean_text(parent.get_text())
                    numbers = extract_numbers(text)
                    if numbers:
                        view_count = str(numbers)
                        break

        # 提取评论数
        comment_count = ""
        comment_elements = soup.find_all(string=lambda x: x and '评论' in x)
        if comment_elements:
            for elem in comment_elements:
                parent = elem.parent
                if parent:
                    text = clean_text(parent.get_text())
                    numbers = extract_numbers(text)
                    if numbers:
                        comment_count = str(numbers)
                        break

        return {
            'title': title,
            'content': content,
            'author': author,
            'publish_time': publish_time,
            'view_count': view_count,
            'comment_count': comment_count,
            'url': url,
            'content_length': len(content),
            'crawl_time': datetime.now().isoformat()
        }

    def crawl_article_detail(self, url: str) -> Optional[Dict[str, Any]]:
        """爬取单篇文章详情"""
        self.logger.info(f"开始爬取文章详情: {url}")

        response = self._make_request(url)
        if not response:
            return None

        response.encoding = 'utf-8'
        article_detail = self._parse_article_detail(response.text, url)

        return article_detail

    def crawl_articles_with_content(self, articles: List[Dict[str, Any]], max_articles: int = 10) -> List[Dict[str, Any]]:
        """爬取文章列表并获取详细内容"""
        self.logger.info(f"开始爬取文章详细内容，最多 {max_articles} 篇")

        detailed_articles = []

        for i, article in enumerate(articles[:max_articles]):
            try:
                self.logger.info(f"爬取第 {i+1}/{min(len(articles), max_articles)} 篇文章")

                # 获取文章详情
                detail = self.crawl_article_detail(article['url'])
                if detail:
                    # 合并列表页和详情页数据
                    merged_article = article.copy()
                    merged_article.update(detail)
                    detailed_articles.append(merged_article)
                else:
                    self.logger.warning(f"无法获取文章详情: {article['url']}")

                # 批量处理间隔
                if (i + 1) % 5 == 0:
                    self.logger.info("批量处理暂停...")
                    random_delay(3, 6)

            except Exception as e:
                self.logger.error(f"爬取文章详情时出错: {e}")
                continue

        self.logger.info(f"成功爬取 {len(detailed_articles)} 篇文章的详细内容")
        return detailed_articles

    def get_statistics(self) -> Dict[str, Any]:
        """获取爬取统计信息"""
        if not self.articles_data:
            return {}

        stats = {
            'total_articles': len(self.articles_data),
            'articles_with_author': len([a for a in self.articles_data if a.get('author')]),
            'articles_with_time': len([a for a in self.articles_data if a.get('publish_time')]),
            'articles_with_content': len([a for a in self.articles_data if a.get('content')]),
            'unique_authors': len(set(a.get('author') for a in self.articles_data if a.get('author'))),
            'total_content_length': sum(a.get('content_length', 0) for a in self.articles_data),
            'crawl_time': datetime.now().isoformat()
        }

        return stats
