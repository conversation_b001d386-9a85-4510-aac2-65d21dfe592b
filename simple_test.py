# -*- coding: utf-8 -*-
"""
简化测试脚本 - 验证爬虫基本功能
"""

import json
from datetime import datetime

# 模拟测试数据
def create_test_data():
    """创建测试数据"""
    test_articles = [
        {
            "title": "年轻人爆买金包银，要面子也要务实",
            "url": "https://www.huxiu.com/article/4550305.html",
            "author": "真故研究室",
            "author_url": "https://www.huxiu.com/member/3283352.html",
            "publish_time": "5分钟前",
            "article_id": "4550305",
            "crawl_time": datetime.now().isoformat()
        },
        {
            "title": "一人公司出海，如何找到百万美金赛道？",
            "url": "https://www.huxiu.com/article/4550833.html",
            "author": "许良©",
            "author_url": "https://www.huxiu.com/member/13377962.html",
            "publish_time": "20分钟前",
            "article_id": "4550833",
            "crawl_time": datetime.now().isoformat()
        },
        {
            "title": "一位厂二代无班可接之后",
            "url": "https://www.huxiu.com/article/4550797.html",
            "author": "镜相工作室",
            "author_url": "https://www.huxiu.com/member/9467135.html",
            "publish_time": "34分钟前",
            "article_id": "4550797",
            "crawl_time": datetime.now().isoformat()
        }
    ]
    return test_articles

def test_data_processing():
    """测试数据处理功能"""
    print("=" * 50)
    print("测试数据处理功能")
    print("=" * 50)
    
    # 导入必要的模块
    try:
        from huxiu_spider import HuxiuSpider
        from utils import *
        
        # 创建爬虫实例
        spider = HuxiuSpider()
        
        # 创建测试数据
        test_data = create_test_data()
        print(f"✓ 创建了 {len(test_data)} 条测试数据")
        
        # 测试数据保存功能
        print("\n测试数据保存功能:")
        
        # JSON保存
        json_file = spider.save_to_json(test_data, "test_output.json")
        if json_file:
            print(f"✓ JSON保存成功: {json_file}")
        
        # CSV保存
        csv_file = spider.save_to_csv(test_data, "test_output.csv")
        if csv_file:
            print(f"✓ CSV保存成功: {csv_file}")
        
        # 摘要报告
        spider.articles_data = test_data  # 设置数据用于统计
        summary_file = spider.save_summary_report(test_data, "test_summary.txt")
        if summary_file:
            print(f"✓ 摘要报告保存成功: {summary_file}")
        
        # 测试统计功能
        print("\n测试统计功能:")
        stats = spider.get_statistics()
        for key, value in stats.items():
            print(f"  {key}: {value}")
        
        # 测试工具函数
        print("\n测试工具函数:")
        
        # 测试时间解析
        test_time = parse_relative_time("5分钟前")
        print(f"✓ 时间解析: '5分钟前' -> {test_time}")
        
        # 测试文本清理
        test_text = clean_text("  这是一个\n\t测试文本  ")
        print(f"✓ 文本清理: '  这是一个\\n\\t测试文本  ' -> '{test_text}'")
        
        # 测试URL标准化
        test_url = normalize_url("/article/123.html")
        print(f"✓ URL标准化: '/article/123.html' -> '{test_url}'")
        
        # 测试文章ID提取
        test_id = extract_article_id("https://www.huxiu.com/article/4550305.html")
        print(f"✓ 文章ID提取: '...article/4550305.html' -> '{test_id}'")
        
        print("\n" + "=" * 50)
        print("✅ 所有数据处理功能测试通过!")
        print("=" * 50)
        
        return True
        
    except ImportError as e:
        print(f"❌ 导入模块失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_file_structure():
    """测试文件结构"""
    print("\n" + "=" * 50)
    print("检查项目文件结构")
    print("=" * 50)
    
    import os
    
    required_files = [
        'huxiu_spider.py',
        'config.py',
        'utils.py',
        'selectors.py',
        'anti_spider.py',
        'requirements.txt',
        'README.md'
    ]
    
    missing_files = []
    for file in required_files:
        if os.path.exists(file):
            print(f"✓ {file}")
        else:
            print(f"❌ {file} (缺失)")
            missing_files.append(file)
    
    # 检查数据目录
    if os.path.exists('data'):
        print("✓ data/ (数据目录)")
    else:
        print("❌ data/ (数据目录缺失)")
        missing_files.append('data/')
    
    if missing_files:
        print(f"\n⚠️  缺失文件: {', '.join(missing_files)}")
        return False
    else:
        print(f"\n✅ 所有必需文件都存在!")
        return True

def main():
    """主测试函数"""
    print(f"虎嗅爬虫简化测试 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 检查文件结构
    structure_ok = test_file_structure()
    
    if structure_ok:
        # 测试数据处理功能
        processing_ok = test_data_processing()
        
        if processing_ok:
            print(f"\n🎉 简化测试完成，基本功能正常!")
            print("\n📝 使用说明:")
            print("1. 运行 'python main.py --mode today' 爬取今日新闻")
            print("2. 运行 'python main.py --mode detail --max-articles 5' 爬取详细内容")
            print("3. 运行 'python test_spider.py' 进行完整测试")
        else:
            print(f"\n❌ 数据处理功能测试失败")
    else:
        print(f"\n❌ 文件结构检查失败")

if __name__ == "__main__":
    main()
