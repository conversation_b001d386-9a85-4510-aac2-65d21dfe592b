# -*- coding: utf-8 -*-
"""
虎嗅新闻爬虫主程序
使用示例
"""

import argparse
from datetime import datetime
from huxiu_spider import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>

def main():
    """主程序"""
    parser = argparse.ArgumentParser(description='虎嗅新闻爬虫')
    parser.add_argument('--mode', choices=['list', 'today', 'detail'], default='today',
                       help='爬取模式: list(文章列表), today(今日新闻), detail(包含详情)')
    parser.add_argument('--max-articles', type=int, default=10,
                       help='最大文章数量（detail模式）')
    parser.add_argument('--output', choices=['json', 'csv', 'excel', 'all'], default='all',
                       help='输出格式')
    parser.add_argument('--filename', type=str, default=None,
                       help='输出文件名（不含扩展名）')
    
    args = parser.parse_args()
    
    print("=" * 60)
    print("🦊 虎嗅新闻爬虫")
    print("=" * 60)
    print(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"爬取模式: {args.mode}")
    print(f"输出格式: {args.output}")
    
    # 创建爬虫实例
    spider = HuxiuSpider()
    
    try:
        # 根据模式爬取数据
        if args.mode == 'list':
            print("\n📰 爬取文章列表...")
            articles = spider.crawl_article_list()
            
        elif args.mode == 'today':
            print("\n📅 爬取今日新闻...")
            articles = spider.crawl_today_news()
            
        elif args.mode == 'detail':
            print(f"\n📖 爬取文章详情（最多{args.max_articles}篇）...")
            # 先获取文章列表
            article_list = spider.crawl_today_news()
            if article_list:
                articles = spider.crawl_articles_with_content(article_list, args.max_articles)
            else:
                print("❌ 无法获取文章列表")
                return
        
        if not articles:
            print("❌ 没有爬取到任何文章")
            return
        
        print(f"\n✅ 成功爬取 {len(articles)} 篇文章")
        
        # 显示前3篇文章的基本信息
        print("\n📋 文章预览:")
        for i, article in enumerate(articles[:3], 1):
            print(f"\n{i}. {article.get('title', '无标题')}")
            print(f"   作者: {article.get('author', '未知')}")
            print(f"   时间: {article.get('publish_time', '未知')}")
            if article.get('content_length'):
                print(f"   内容长度: {article['content_length']} 字符")
        
        if len(articles) > 3:
            print(f"\n... 还有 {len(articles) - 3} 篇文章")
        
        # 保存数据
        print(f"\n💾 保存数据...")
        
        if args.output == 'all':
            # 保存所有格式
            exported_files = spider.export_all_formats(articles, args.filename)
            print("✅ 已导出以下文件:")
            for format_type, filepath in exported_files.items():
                print(f"   {format_type.upper()}: {filepath}")
        else:
            # 保存指定格式
            if args.output == 'json':
                filepath = spider.save_to_json(articles, 
                    f"{args.filename}.json" if args.filename else None)
            elif args.output == 'csv':
                filepath = spider.save_to_csv(articles, 
                    f"{args.filename}.csv" if args.filename else None)
            elif args.output == 'excel':
                filepath = spider.save_to_excel(articles, 
                    f"{args.filename}.xlsx" if args.filename else None)
            
            if filepath:
                print(f"✅ 文件已保存: {filepath}")
        
        # 显示统计信息
        print(f"\n📊 统计信息:")
        stats = spider.get_statistics()
        for key, value in stats.items():
            print(f"   {key}: {value}")
        
        print(f"\n🎉 爬取完成! - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
    except KeyboardInterrupt:
        print(f"\n⏹️  用户中断爬取")
    except Exception as e:
        print(f"\n❌ 爬取过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
